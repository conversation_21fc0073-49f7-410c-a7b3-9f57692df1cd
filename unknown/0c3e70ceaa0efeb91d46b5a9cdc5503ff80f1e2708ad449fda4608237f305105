namespace :logs do
  desc "Clean all log files in scripts/logs directory"
  task clean: :environment do
    logs_dir = Rails.root.join(Rails.env.production? ? "external_scripts" : "scripts", "logs")

    if Dir.exist?(logs_dir)
      log_files = Dir.glob(File.join(logs_dir, "*.log"))

      if log_files.any?
        puts "Cleaning #{log_files.count} log files from #{logs_dir}..."
        log_files.each do |file|
          File.delete(file)
          puts "Deleted: #{File.basename(file)}"
        end
        puts "Log cleanup completed!"
      else
        puts "No log files found in #{logs_dir}"
      end
    else
      puts "Logs directory #{logs_dir} does not exist"
    end
  end

  desc "Rotate logs and manage size (keep total under 10MB)"
  task rotate: :environment do
    # Access environment variables
    logs_dir = Rails.root.join(Rails.env.production? ? "external_scripts" : "scripts", "logs")
    max_size_mb = ENV.fetch('MAX_LOG_SIZE_MB', '10').to_i
    max_total_size = max_size_mb * 1024 * 1024 # Convert MB to bytes

    puts "Environment: #{Rails.env}"
    puts "Log directory: #{logs_dir}"
    puts "Max size limit: #{max_size_mb} MB"

    unless Dir.exist?(logs_dir)
      puts "Logs directory #{logs_dir} does not exist"
      next
    end

    # Get all log files (current and rotated)
    all_log_files = Dir.glob(File.join(logs_dir, "*.log*")).sort_by { |f| File.mtime(f) }

    if all_log_files.empty?
      puts "No log files found in #{logs_dir}"
      next
    end

    # Calculate current total size
    total_size = all_log_files.sum { |file| File.size(file) }
    puts "Current total log size: #{(total_size / 1024.0 / 1024.0).round(2)} MB"

    # Rotate current log files (add timestamp)
    current_log_files = Dir.glob(File.join(logs_dir, "*.log")).reject { |f| f.match?(/\.\d{8}_\d{6}\.log/) }
    timestamp = Time.current.strftime("%Y%m%d_%H%M%S")

    current_log_files.each do |log_file|
      next unless File.exist?(log_file) && File.size(log_file) > 0

      base_name = File.basename(log_file, ".log")
      rotated_name = "#{base_name}.#{timestamp}.log"
      rotated_path = File.join(logs_dir, rotated_name)

      File.rename(log_file, rotated_path)
      puts "Rotated: #{File.basename(log_file)} -> #{rotated_name}"
    end

    # Recalculate with rotated files
    all_log_files = Dir.glob(File.join(logs_dir, "*.log*")).sort_by { |f| File.mtime(f) }
    total_size = all_log_files.sum { |file| File.size(file) }

    # Remove oldest files if total size exceeds limit
    if total_size > max_total_size
      puts "Total size (#{(total_size / 1024.0 / 1024.0).round(2)} MB) exceeds limit (10 MB)"
      puts "Removing oldest log files..."

      # Sort by modification time (oldest first)
      files_by_age = all_log_files.sort_by { |f| File.mtime(f) }

      files_by_age.each do |file|
        break if total_size <= max_total_size

        file_size = File.size(file)
        File.delete(file)
        total_size -= file_size
        puts "Deleted: #{File.basename(file)} (#{(file_size / 1024.0).round(2)} KB)"
      end
    end

    # Final size report
    remaining_files = Dir.glob(File.join(logs_dir, "*.log*"))
    final_size = remaining_files.sum { |file| File.size(file) }
    puts "Final total log size: #{(final_size / 1024.0 / 1024.0).round(2)} MB"
    puts "Remaining files: #{remaining_files.count}"
  end

  desc "Show log directory status and size information"
  task status: :environment do
    logs_dir = Rails.root.join(Rails.env.production? ? "external_scripts" : "scripts", "logs")

    unless Dir.exist?(logs_dir)
      puts "Logs directory #{logs_dir} does not exist"
      next
    end

    all_log_files = Dir.glob(File.join(logs_dir, "*.log*"))

    if all_log_files.empty?
      puts "No log files found in #{logs_dir}"
      next
    end

    puts "Log Directory Status:"
    puts "=" * 50
    puts "Directory: #{logs_dir}"
    puts "Total files: #{all_log_files.count}"

    total_size = 0
    current_logs = []
    rotated_logs = []

    all_log_files.sort.each do |file|
      size = File.size(file)
      total_size += size
      mtime = File.mtime(file).strftime("%Y-%m-%d %H:%M:%S")

      if file.match?(/\.\d{8}_\d{6}\.log/)
        rotated_logs << { name: File.basename(file), size: size, mtime: mtime }
      else
        current_logs << { name: File.basename(file), size: size, mtime: mtime }
      end
    end

    puts "\nCurrent log files:"
    if current_logs.any?
      current_logs.each do |log|
        puts "  #{log[:name]} - #{(log[:size] / 1024.0).round(2)} KB - #{log[:mtime]}"
      end
    else
      puts "  None"
    end

    puts "\nRotated log files:"
    if rotated_logs.any?
      rotated_logs.each do |log|
        puts "  #{log[:name]} - #{(log[:size] / 1024.0).round(2)} KB - #{log[:mtime]}"
      end
    else
      puts "  None"
    end

    puts "\nTotal size: #{(total_size / 1024.0 / 1024.0).round(2)} MB"
    puts "Size limit: 10.00 MB"

    if total_size > 10 * 1024 * 1024
      puts "⚠️  WARNING: Total size exceeds 10MB limit!"
    else
      puts "✅ Size is within limit"
    end
  end
end
