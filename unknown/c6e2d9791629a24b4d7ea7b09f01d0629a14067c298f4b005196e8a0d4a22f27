class LogRotationJob < ApplicationJob
  queue_as :default

  def perform
    Rails.logger.info "Starting daily log rotation task"

    logs_dir = Rails.root.join(Rails.env.production? ? "external_scripts" : "scripts", "logs")
    max_total_size = 10 * 1024 * 1024 # 10MB in bytes

    unless Dir.exist?(logs_dir)
      Rails.logger.warn "Logs directory #{logs_dir} does not exist"
      return
    end

    # Get all log files (current and rotated)
    all_log_files = Dir.glob(File.join(logs_dir, "*.log*")).sort_by { |f| File.mtime(f) }

    if all_log_files.empty?
      Rails.logger.info "No log files found in #{logs_dir}"
      return
    end

    # Calculate current total size
    total_size = all_log_files.sum { |file| File.size(file) }
    Rails.logger.info "Current total log size: #{(total_size / 1024.0 / 1024.0).round(2)} MB"

    # Rotate current log files (add timestamp)
    current_log_files = Dir.glob(File.join(logs_dir, "*.log")).reject { |f| f.match?(/\.\d{8}_\d{6}\.log/) }
    timestamp = Time.current.strftime("%Y%m%d_%H%M%S")

    rotated_count = 0
    current_log_files.each do |log_file|
      next unless File.exist?(log_file) && File.size(log_file) > 0

      base_name = File.basename(log_file, ".log")
      rotated_name = "#{base_name}.#{timestamp}.log"
      rotated_path = File.join(logs_dir, rotated_name)

      File.rename(log_file, rotated_path)
      Rails.logger.info "Rotated: #{File.basename(log_file)} -> #{rotated_name}"
      rotated_count += 1
    end

    # Recalculate with rotated files
    all_log_files = Dir.glob(File.join(logs_dir, "*.log*")).sort_by { |f| File.mtime(f) }
    total_size = all_log_files.sum { |file| File.size(file) }

    # Remove oldest files if total size exceeds limit
    deleted_count = 0
    if total_size > max_total_size
      Rails.logger.info "Total size (#{(total_size / 1024.0 / 1024.0).round(2)} MB) exceeds limit (10 MB)"
      Rails.logger.info "Removing oldest log files..."

      # Sort by modification time (oldest first)
      files_by_age = all_log_files.sort_by { |f| File.mtime(f) }

      files_by_age.each do |file|
        break if total_size <= max_total_size

        file_size = File.size(file)
        File.delete(file)
        total_size -= file_size
        Rails.logger.info "Deleted: #{File.basename(file)} (#{(file_size / 1024.0).round(2)} KB)"
        deleted_count += 1
      end
    end

    # Final size report
    remaining_files = Dir.glob(File.join(logs_dir, "*.log*"))
    final_size = remaining_files.sum { |file| File.size(file) }

    Rails.logger.info "Log rotation completed:"
    Rails.logger.info "  - Rotated files: #{rotated_count}"
    Rails.logger.info "  - Deleted files: #{deleted_count}"
    Rails.logger.info "  - Final total size: #{(final_size / 1024.0 / 1024.0).round(2)} MB"
    Rails.logger.info "  - Remaining files: #{remaining_files.count}"
  rescue => e
    Rails.logger.error "Log rotation job failed: #{e.message}"
    Rails.logger.error e.backtrace.join("\n")
    raise e
  end
end
