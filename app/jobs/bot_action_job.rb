class BotActionJob < ApplicationJob
  queue_as :default

  def perform(action, bot_id, user_id)
    @bot = Script.find_by(id: bot_id)
    @user = User.find_by(id: user_id)

    return unless @bot && @user

    # Ensure the bot belongs to the user
    return unless @bot.user_id == @user.id

    case action
    when 'start'
      start_bot
    when 'stop'
      stop_bot
    when 'restart'
      restart_bot
    else
      Rails.logger.error("Unknown bot action: #{action}")
    end
  end

  private

  def start_bot
    response = ScriptManagerService.start_script(@bot.id)

    if response && response.success?
      # Broadcast success message
      broadcast_result('notice', "Bot #{@bot.type.downcase} was successfully started.")
      # Broadcast status update
      broadcast_status_update
    else
      # Broadcast error message
      broadcast_result('alert', "Failed to start bot #{@bot.type.downcase}.")
      # Still broadcast status update to show current state
      broadcast_status_update
    end
  end

  def stop_bot
    response = ScriptManagerService.stop_script(@bot.id)

    if response && response.success?
      # Broadcast success message
      broadcast_result('notice', "Bot #{@bot.type.downcase} was successfully stopped.")
      # Broadcast status update
      broadcast_status_update
    else
      # Broadcast error message
      broadcast_result('alert', "Failed to stop bot #{@bot.type.downcase}.")
      # Still broadcast status update to show current state
      broadcast_status_update
    end
  end

  def restart_bot
    # First stop the bot
    stop_response = ScriptManagerService.stop_script(@bot.id)

    if stop_response && stop_response.success?
      Rails.logger.info("Bot #{@bot.id} stopped successfully, now starting...")

      # Wait a moment before starting to ensure clean shutdown
      sleep(2)

      # Then start the bot
      start_response = ScriptManagerService.start_script(@bot.id)

      if start_response && start_response.success?
        # Broadcast success message
        broadcast_result('notice', "Bot #{@bot.type.downcase} was successfully restarted.")
        # Broadcast status update
        broadcast_status_update
      else
        # Broadcast error message
        broadcast_result('alert', "Bot #{@bot.type.downcase} was stopped but failed to start again.")
        # Still broadcast status update to show current state
        broadcast_status_update
      end
    else
      # If stop failed, don't attempt to start
      broadcast_result('alert', "Failed to restart bot #{@bot.type.downcase} - could not stop the bot.")
      # Still broadcast status update to show current state
      broadcast_status_update
    end
  end

  def broadcast_status_update
    # Get current status from script manager
    status_response = ScriptManagerService.script_status(@bot.id)

    status_class = 'bg-gray-600 text-gray-200'
    status_text = 'Unknown'

    if status_response && status_response.success?
      status_data = status_response.parsed_response
      case status_data['status']
      when 'running'
        status_class = 'bg-green-800 text-green-200'
        status_text = 'Running'
      when 'stopped'
        status_class = 'bg-red-800 text-red-200'
        status_text = 'Stopped'
      when 'not_found'
        status_class = 'bg-yellow-800 text-yellow-200'
        status_text = 'Not Started'
      end
    else
      status_class = 'bg-red-800 text-red-200'
      status_text = 'Error'
    end

    # Broadcast the status update
    Turbo::StreamsChannel.broadcast_update_to(
      "bot_#{@bot.id}_status",
      target: "bot_status_#{@bot.id}",
      partial: 'bots/bot_status_content',
      locals: {
        bot: @bot,
        status_class: status_class,
        status_text: status_text
      }
    )
  end
end
