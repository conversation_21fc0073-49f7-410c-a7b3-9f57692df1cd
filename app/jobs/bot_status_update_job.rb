class BotStatusUpdateJob < ApplicationJob
  queue_as :default

  def perform(bot_id)
    @bot = Script.find_by(id: bot_id)
    return unless @bot

    broadcast_status_update
  end

  private

  def broadcast_status_update
    # Get current status from script manager
    status_response = ScriptManagerService.script_status(@bot.id)
    
    status_class = 'bg-gray-600 text-gray-200'
    status_text = 'Unknown'
    
    if status_response && status_response.success?
      status_data = status_response.parsed_response
      case status_data['status']
      when 'running'
        status_class = 'bg-green-800 text-green-200'
        status_text = 'Running'
      when 'stopped'
        status_class = 'bg-red-800 text-red-200'
        status_text = 'Stopped'
      when 'not_found'
        status_class = 'bg-yellow-800 text-yellow-200'
        status_text = 'Not Started'
      end
    else
      status_class = 'bg-red-800 text-red-200'
      status_text = 'Error'
    end

    # Broadcast the status update
    Turbo::StreamsChannel.broadcast_update_to(
      "bot_#{@bot.id}_status",
      target: "bot_status_#{@bot.id}",
      partial: 'bots/bot_status_content',
      locals: { 
        bot: @bot,
        status_class: status_class,
        status_text: status_text
      }
    )
  end
end
