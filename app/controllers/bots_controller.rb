class BotsController < ApplicationController
  before_action :authenticate_user!
  before_action :set_bot, only: [:show, :edit, :update, :destroy, :start, :stop, :status, :logs]

  def index
    @bots = current_user.scripts

    # Trigger initial status updates for all bots
    @bots.each do |bot|
      BotStatusUpdateJob.perform_later(bot.id)
    end
  end

  def show
  end

  def new
    @bot_type = params[:type]&.downcase&.capitalize || 'Copier'
    @bot = @bot_type.constantize.new(user: current_user)
    @from_accounts = current_user.accounts.ctrader
    @to_accounts = current_user.accounts.mt5
  end

  def edit
    @from_accounts = current_user.accounts.ctrader
    @to_accounts = current_user.accounts.mt5
  end

  def create
    @bot_type = bot_params[:type]&.downcase&.capitalize

    if @bot_type == 'Copier'
      @bot = Copier.new(
        user: current_user,
        from_account_id: bot_params[:from_account_id]
      )
    else
      # For future trader script type
      @bot = Script.new(
        user: current_user,
        type: @bot_type
      )
    end

    if @bot.save
      # Add to_accounts for copier bots
      if @bot_type == 'Copier' && bot_params[:to_account_ids].present?
        bot_params[:to_account_ids].each do |account_id|
          @bot.to_accounts << Account.find(account_id) if account_id.present?
        end
      end

      redirect_to bots_path, notice: 'Bot was successfully created.'
    else
      @from_accounts = current_user.accounts.ctrader
      @to_accounts = current_user.accounts.mt5
      render :new, status: :unprocessable_entity
    end
  end

  def update
    ActiveRecord::Base.transaction do
      if @bot.update(bot_params.except(:type, :to_account_ids))

        # Update to_accounts for copier bots
        if @bot.type == 'Copier' && bot_params[:to_account_ids].present?
          @bot.to_accounts.clear
          bot_params[:to_account_ids].each do |account_id|
            @bot.to_accounts << Account.find(account_id) if account_id.present?
          end
        end

        redirect_to bots_path, notice: 'Bot was successfully updated.'
      else
        @from_accounts = current_user.accounts.ctrader
        @to_accounts = current_user.accounts.mt5
        render :edit, status: :unprocessable_entity
      end
    end
  end

  def destroy
    @bot.destroy
    redirect_to bots_path, notice: 'Bot was successfully deleted.'
  end

  def start
    # Queue the job to start the bot
    BotActionJob.perform_later('start', @bot.id, current_user.id)

    # Render Turbo Stream response directly for immediate feedback
    render turbo_stream: turbo_stream.update("flash_messages",
      partial: "shared/flash_message",
      locals: { type: 'notice', message: 'Request received. Starting bot...' }
    )
  end

  def stop
    # Queue the job to stop the bot
    BotActionJob.perform_later('stop', @bot.id, current_user.id)

    # Render Turbo Stream response directly for immediate feedback
    render turbo_stream: turbo_stream.update("flash_messages",
      partial: "shared/flash_message",
      locals: { type: 'notice', message: 'Request received. Stopping bot...' }
    )
  end

  def status
    response = ScriptManagerService.script_status(@bot.id)
    render json: response
  end

  def logs
    @lines = params[:lines].present? ? params[:lines].to_i : 100
    @response = ScriptManagerService.script_logs(@bot.id, @lines)

    if @response && @response.success?
      @logs = @response.parsed_response
    else
      flash.now[:alert] = 'Failed to fetch logs. The script may not be running or there was an error.'
      @logs = { "stdout" => "", "stderr" => "" }
    end
  end

  def refresh_status
    bot_ids = params[:bot_ids] || []

    # Filter to only user's bots for security
    user_bot_ids = current_user.scripts.where(id: bot_ids).pluck(:id)

    # Trigger status updates for each bot
    user_bot_ids.each do |bot_id|
      BotStatusUpdateJob.perform_later(bot_id)
    end

    head :ok
  end

  private

  def set_bot
    @bot = current_user.scripts.find(params[:id])
  end

  def bot_params
    params.require(:script).permit(
      :type, :from_account_id, to_account_ids: []
    )
  end
end
